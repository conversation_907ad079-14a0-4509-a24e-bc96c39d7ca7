# Multi-stage Dockerfile for optimized Go GUI application builds
# Stage 1: Base builder image with GUI dependencies
FROM golang:1.21-bullseye AS base-builder

# Install GUI development dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-dev \
    xorg-dev \
    libx11-dev \
    libxrandr-dev \
    libxinerama-dev \
    libxcursor-dev \
    libxi-dev \
    libxxf86vm-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Set up Go environment for GUI builds
ENV CGO_ENABLED=1
ENV GOOS=linux

# Create app directory
WORKDIR /app

# Stage 2: Dependency layer (cached)
FROM base-builder AS deps

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies with cache mount
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

# Stage 3: Application builder
FROM deps AS builder

# Copy source code
COPY . .

# Build the application with cache mounts
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go build -ldflags="-s -w" -o gallerific ./cmd/app

# Stage 4: Runtime preparation
FROM ubuntu:22.04 AS runtime-prep

# Install runtime dependencies for GUI applications
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libx11-6 \
    libxrandr2 \
    libxinerama1 \
    libxcursor1 \
    libxi6 \
    libxxf86vm1 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -s /bin/bash appuser

# Stage 5: Final runtime image
FROM runtime-prep AS final

WORKDIR /app

# Copy the binary from builder
COPY --from=builder /app/gallerific .
COPY --from=builder /app/assets ./assets

# Set ownership
RUN chown -R appuser:appuser /app

USER appuser

# Set display environment for GUI
ENV DISPLAY=:0

ENTRYPOINT ["./gallerific"]
